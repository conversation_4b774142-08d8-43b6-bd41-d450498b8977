from typing import List, Optional

from acva_ai.llm.helpers import parse_json_output
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.utils.usage import ResponseUsage

EXTRACT_MEDICAL_TERMS_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
Your task is to extract all the references to patient medical terms, symptoms, diseases, syndromes.

Because this is a transcript from a speech-to-text software we want to make sure that all the names of medical terms, diseases, symptoms are correctly identified, even if they are not spelled correctly or if there are minor errors in the transcript.

You must extract all the named medical terms, diseases, symptoms or related text (even if they are discussed without any particular name)

This is the transcript:
{transcript}

Extract all the references to patient medical terms, symptoms, diseases, syndromes. 

Respond in {language} 

Make a list of all the medical terms mentioned in the transcript in a JSON format, with the following structure:
JSON:
- medical_term_name: <name of the medical term in {language}> if spelled, if not, None
- medical_term_context: <context / explanations in which the medical term is mentioned, the symptoms or any other relevant information about that particular medical term in the discussion>

Return the JSON list without any other commentary.
"""


async def extract_medical_terms(
    transcript: str,
    llm_orchestrator: LLMOrchestrator,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> List:
    """
    Extract medical terms from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        List of extracted medical terms
    """
    check_prompt = EXTRACT_MEDICAL_TERMS_PROMPT.format(
        transcript=transcript, language=language
    )
    response = await llm_orchestrator.call_llm(
        prompt=check_prompt, response_usage=response_usage
    )
    response_dict = await parse_json_output(response)

    results = []
    for item in response_dict or []:
        name = item.get("medical_term_name", None)
        context = item.get("medical_term_context", None)
        if context:
            results.append([name, context])

    return results
