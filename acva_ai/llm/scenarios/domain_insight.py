import asyncio
from typing import Optional

from acva_ai._params import DEFAULT_LLM_PROVIDER
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

DECIDE_INSIGHT_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 

We want to extract all information about <{insight_name}>. You must decide if the visit has any mentions about it, if it was discussed or we have any relevant information.

Transcript:
'''
{transcript}
'''

This is the info we are interested in for <{insight_name}>:
'''
{insight_extraction_prompt}
'''

You must decide if we have sufficient information to extract for <{insight_name}>. If we have, respond with `true`, if not, respond with `false`
Respond only with `true` or `false`, without any other commentary.
"""


EXTRACT_INSIGHT_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
We want to extract all information about <{insight_name}>. You must select all relevant information..

Transcript:
'''
{transcript}
'''

Medication list:
'''
{medication_list}
'''

Medical term list:
'''
{medical_term_list}
'''

This is the info we are interested in for <{insight_name}>:
'''
{insight_extraction_prompt}
'''

Extract the information according to the provided instructions.

Return the information following the style:
'''
{reporting_style}
'''

IMPORTANT: Only extract information related to <{insight_name}>, not about other topics. Keep it strictly related to the topic.
IMPORTANT: Use specific medical jargon and express the information in a professional manner.
IMPORTANT: Respond in {language}

Respond only with the extracted information without any other commentary.
"""


# TODO Maybe remove medication and medical term lists from the prompt
async def extract_insight(
    transcript: str,
    insight_name: str,
    insight_extraction_prompt: str,
    llm_orchestrator: LLMOrchestrator,
    visit_report: VisitReport,
    reporting_style: Optional[str] = None,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Extract domain insights from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        insight_name: Name of the insight to extract
        insight_extraction_prompt: Prompt for extracting the insight
        reporting_style: Style for reporting
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Extracted insight as string
    """
    # check_prompt = DECIDE_INSIGHT_PROMPT.format(
    #     transcript=transcript,
    #     insight_name=insight_name,
    #     insight_extraction_prompt=insight_extraction_prompt,
    #     language=language,
    # )
    #
    # Some models we can keep as GPT4-o-mini for simple tasks but for now let's keep it like this
    # response = await call_llm_async(
    #     check_prompt, response_usage=response_usage, model_id="gpt-4o-mini"
    # )
    # if (
    #     not "true" in response.lower()
    # ):  # Makeshift boolean check. I prefer this to the constrained format of the API specs.
    #     print(f"Insight considered not relevant: {insight_name}")
    #     explanation = await call_llm_async(
    #         EXPLANATION_PROMPT.format(
    #             transcript=transcript,
    #             insight_name=insight_name,
    #             insight_extraction_prompt=insight_extraction_prompt,
    #             language=language,
    #         ),
    #         response_usage=response_usage,
    #     )
    #     # Return a string instead of a dictionary
    #     return f"Nu există informații relevante pentru {insight_name}. {explanation}"

    if visit_report.medications:
        medication_list = [
            (
                medication.extracted_rag_name
                if medication.extracted_rag_name == ""
                else medication.medication_name
            )
            for medication in visit_report.medications
        ]
    else:
        medication_list = []

    if visit_report.medical_terms:
        medical_term_list = [
            medical_term.medical_term_name
            for medical_term in visit_report.medical_terms
        ]
    else:
        medical_term_list = []

    extract_prompt = EXTRACT_INSIGHT_PROMPT.format(
        transcript=transcript,
        insight_name=insight_name,
        insight_extraction_prompt=insight_extraction_prompt,
        language=language,
        reporting_style=reporting_style,
        medication_list=medication_list,
        medical_term_list=medical_term_list,
    )

    response = await llm_orchestrator.call_llm(
        prompt=extract_prompt, response_usage=response_usage
    )

    return response


def test():
    transcript = """
    Cum te numești? (ONIM)
    Tu Bogdan.
    Vârsta cronologică?
    39 de ani (vârstă cronologică).
    Ok, ia să-mi spui, vreo problemă la nivelul abdomenului?
    Câteodată mă mai balonez, ceea ce poate indica o stare de mal.
    Am înțeles, operație de debirdare la burtă?
    Nu, niciodată. (NULIGEST)
    Ok, hai să vedem.
    Lobul stâng 6 cm, lobul drept 14 cm, aspect omogen, hiperecogen, cu atenuare posterioară. Fără dilatații, venă portă normală. Aici vorbim de HEPATIC. Da.
    Ok, trebuie să specific.
    Colecist, am înțeles, gata, organul. Este posibil să fie afectat de colecistolitiază.
    Colecistolitiază. Colecistolitiază. Pereți normotip, conținut transonic, cu prezența unei imagini hiperecogene, bine delimitată, cu diametru de 2,3 cm, cu con de umbră posterior. Splina, diametru anteroposterior de 14 cm, omogenă, normoechogenă. Rinic drept, sit normal situat, contur normal, la nivel medular prezența unor imagini punctiforme hiperecogene, fără con de umbră posterior, fără dilatații ale sistemului pielocalicial. Rinichi stâng, poziție normală, contur normal, cu prezența la nivel medular a unei imagini transonice, bine delimitată, conținut transonic, de 3,3 cm, fără dilatarea sistemului pielocaliceal. Vezica urinară semireplece, conținut transonic, fără formațiuni protruzive. Prostată omogenă, vascularizație normală, cu prezența unei imagini mai hiperecogene situate central, cu diametru de 2,3 cm, fără semne de prostatomegalie. Și există vreo EPICRIZ?
    Concluzie: EPICRIZ.
    Avem o steatoză hepatică gradul 2, litiază veziculară, microlitiază renală cu rinichi polichistic stâng.
    Am încercat o patologie, știi?
    Ca să nu-mi dai mal.
    """

    insight_name = "COLECIST"
    insight_extraction_prompt = """
    COLECIST
    • Perete: … mm Formă (cudat/normal): …
    • Conținut: nămol/calculi/limpede Formațiuni peri-colecistice: …
    
    Date input (ce vei primi mai jos):
	1.	Instrucțiuni generale – reguli de stil și completare pentru continut;
	2.	Instrucțiuni completare continut – indicații detaliate pentru  rubrică;
	3.	Structura  – scheletul pe care îl completezi. Exemplele sunt doar orientative; include-le doar dacă există date în transcript.

    —————————————————————————————
    
    Instrucțiuni generale
        1.	Instrucțiunile de mai jos fac parte integrantă din acest prompt; Completeaza exclusiv datele din sectiunea de jos si nimic altceva.
        3.	Terminologie medicală în limba română (ex. „colelitiază”, „steatoză hepatică”);
        4.	Foloseste o exprimare clară, concisă, adecvată unui raport medical;
        5.	Păstrează ordinea exactă a rubricilor din schelet;
        6.	După redactare, verifică:
        •	S-au respectat toate instrucțiunile?
        •	Lista din „Tratament și Intervenții → Prescripție medicală” conține toate medicamentele/procedurile menționate în transcript. Dacă lipsește ceva, revizuiește;
        7.	Nu introduce termeni sau rubrici ce nu se regasesc in transcript. Nu introduce N/A sau "nementionat" sau alte sinonime. Daca nu gasesti continut, nu pune in raport placeholdere si nu pune campul respectiv deloc (adica sa nu apara denumiri de campuri fara valori. Neaprat sa iei in considerare "Deciziile de completare" de la "Instructiuni completare continut"
        9.	Verifica dupa ce ai generat nota ca ai respectat aceste instructiuni de jos si corecteaza daca este cazul!
    
    —————————————————————————————
    
    Instrucțiuni completare continut
    1. Alte detalii in afara de cele din structura schelet de jos – doar dacă apar în transcript
    2. Decizii de luat in completare:
        1. Daca in sctructura sunt mentionate valori de tipul “regulat/neregulat” , in cazul in care nu se gaseste in transcript nimic, atunci valoarea default este cea normala. Adica “regulat” sau “normal” sau “absent”.
        2. Pentru campurile default vei avea in paranteza, in dreapta campului default (d) . Ex: normal(d) / dilatat , adica normal este campul default.
        3. Toate campurile care au astfel de valori trebuie sa fie prezente , adica completate OBLIGATORIU.
        4. Daca este mentionata valoarea default sau daca nu este mentionat nimic , atunci se va completa valoarea default. Altfel , se va completa cealalta valoare si orice alte informatii mai reies.
        5. Campurile care nu au detalii valorice, daca nu sunt mentionate, nu completa campul.
    """

    result = asyncio.run(
        extract_insight(
            transcript=transcript,
            insight_name=insight_name,
            insight_extraction_prompt=insight_extraction_prompt,
            reporting_style="Medical Report",
            llm_orchestrator=LLMOrchestrator(DEFAULT_LLM_PROVIDER),
            visit_report=VisitReport(task_id="test_task_id"),
        )
    )


if __name__ == "__main__":
    test()
