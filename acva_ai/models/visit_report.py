from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field

from acva_ai.models.domain_insight import DomainInsight
from acva_ai.models.medical_term import MedicalTerm
from acva_ai.models.medication import Medication
from acva_ai.utils.usage import ResponseUsage


class VisitReport(BaseModel):
    """
    The object that contains all the processed data corresponding to a Visit.
    Status tracking is handled separately by ProcessingStatus.
    """

    # Identification
    task_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Core Results
    raw_transcript: Optional[str] = None
    transcript: Optional[str] = None
    transcript_observations: List[str] = Field(default_factory=list)
    grammar_observations: List[str] = Field(default_factory=list)
    medical_report: Dict[str, Any] = Field(default_factory=dict)

    # Speaker Diarization Data
    speakers_data: Optional[List[Dict[str, str]]] = None

    # Extracted Information
    medications: List[Medication] = Field(default_factory=list)
    medical_terms: List[MedicalTerm] = Field(default_factory=list)
    domain_insights: List[DomainInsight] = Field(default_factory=list)

    # Generated Content
    html_note: Optional[str] = None

    # Usage Tracking
    stage_timings: Dict[str, float] = Field(default_factory=dict)
    total_processing_time: Optional[float] = None

    metadata: Optional[Dict[str, Any]] = None

    response_usage: Optional[ResponseUsage] = None

    def dict(self, *args, **kwargs):
        """Override dict method to ensure datetime serialization"""
        d = super().dict(*args, **kwargs)
        # Convert datetime objects to ISO format strings
        if d.get("created_at"):
            d["created_at"] = d["created_at"].isoformat()
        return d
