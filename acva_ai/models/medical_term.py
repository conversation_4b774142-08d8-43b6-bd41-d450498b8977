from typing import List, Optional

from pydantic import BaseModel


class MedicalTerm(BaseModel):
    medical_term_name: Optional[str] = None
    medical_term_context: Optional[str] = None
    verification_source: Optional[str] = (
        None  # Source of verification: "RAG" or "openai_initial_text"
    )

    # We might add additional parameters if needed


class MedicalTermList(BaseModel):
    medical_terms: List[MedicalTerm]
