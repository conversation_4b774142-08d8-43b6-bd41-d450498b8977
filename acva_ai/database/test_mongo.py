import logging
import uuid
from datetime import datetime

from acva_ai.database.mongo import mongo_instance
from acva_ai.models.domain_insight import DomainInsight
from acva_ai.models.medical_term import MedicalTerm
from acva_ai.models.medication import Medication
from acva_ai.models.processing_status import (
    PipelineStageStatus,
    ProcessingStatus,
    StageResult,
)
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_visit_report() -> VisitReport:
    """Create a test VisitReport object with sample data"""
    task_id = str(uuid.uuid4())

    # Create sample data
    medications = [
        Medication(
            denumire="Paracetamol",
            prospect="Medicament pentru durere și febră",
            referinta="https://example.com/paracetamol",
        ),
        Medication(
            denumire="Ibuprofen",
            prospect="Anti-inflamator nesteroidian",
            referinta="https://example.com/ibuprofen",
        ),
    ]

    medical_terms = [
        MedicalTerm(
            medical_term_name="Migrenă",
            medical_term_context="Durere de cap severă",
            verification_source="openai_initial_text",
        )
    ]

    domain_insights = [
        DomainInsight(
            insight_name="Diagnostic",
            insight_extraction_prompt="Extrage diagnosticul",
            insight_content="Migrenă cronică",
        )
    ]

    # Create test VisitReport
    visit_report = VisitReport(
        task_id=task_id,
        raw_transcript="Pacientul acuză dureri de cap severe...",
        transcript="Pacientul prezintă simptome de migrenă...",
        grammar_observations=["Corecție gramaticală aplicată"],
        medical_report={
            "diagnostic": "Migrenă cronică",
            "tratament": "Paracetamol și Ibuprofen",
        },
        medications=medications,
        medical_terms=medical_terms,
        domain_insights=domain_insights,
        html_note="<h1>Raport Medical</h1><p>Diagnostic: Migrenă cronică</p>",
        response_usage=ResponseUsage(),
    )

    return visit_report


def create_test_processing_status(task_id: str) -> ProcessingStatus:
    """Create a test ProcessingStatus object"""
    status = ProcessingStatus(task_id=task_id)

    # Add test stage statuses
    status.start_stage("transcription")
    status.complete_stage("transcription", "Transcription completed")

    status.start_stage("grammar_check")
    status.complete_stage("grammar_check", "Grammar check completed")

    status.start_stage("medication_verification")
    status.fail_stage(
        "medication_verification",
        Exception("API timeout"),
        "Timeout while verifying medications",
    )

    return status


def run_mongo_test():
    """Run test scenarios for MongoDB"""
    try:
        logger.info("Initializing MongoDB...")
        mongo_instance.initialize()

        # Create test data
        logger.info("Creating test data...")
        visit_report = create_test_visit_report()
        processing_status = create_test_processing_status(visit_report.task_id)

        # Save audio_processing status
        logger.info("Saving audio_processing status...")
        mongo_instance.update_processing_status(
            visit_report.task_id, processing_status.dict()
        )
        logger.info(f"Saved audio_processing status for task {visit_report.task_id}")

        # Save visit report
        logger.info("Saving visit report...")
        mongo_instance.save_visit_report(visit_report.task_id, visit_report.dict())
        logger.info(f"Saved visit report for task {visit_report.task_id}")

        # Verify saved data
        logger.info("Verifying saved data...")

        # Get and verify audio_processing status
        saved_status = mongo_instance.get_processing_status(visit_report.task_id)
        assert saved_status is not None, "Processing status not found"
        assert saved_status.overall_status == "queued"  # Initial status
        assert len(saved_status.successful_stages) == 2  # Two completed stages
        assert len(saved_status.failed_stages) == 1  # One failed stage
        logger.info("✓ Processing status verified")

        # Get and verify visit report
        saved_report = mongo_instance.get_visit_report(visit_report.task_id)
        assert saved_report is not None, "Visit report not found"
        assert len(saved_report.medications) == 2
        assert len(saved_report.medical_terms) == 1
        logger.info("✓ Visit report verified")

        # Test listing audio_processing statuses
        logger.info("Testing list operation...")
        status_list = mongo_instance.list_processing_statuses(limit=10)
        assert status_list["items"], "No audio_processing statuses found"
        logger.info(f"✓ Found {len(status_list['items'])} audio_processing statuses")

        # Test filtering by status
        logger.info("Testing status filtering...")
        queued_statuses = mongo_instance.list_processing_statuses(
            status="queued", limit=5
        )
        assert queued_statuses["items"], "No queued statuses found"
        logger.info(f"✓ Found {len(queued_statuses['items'])} queued statuses")

        logger.info("All tests completed successfully!")

        # Return the task_id for reference
        return visit_report.task_id

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        raise


if __name__ == "__main__":
    task_id = run_mongo_test()
    print(f"\nTest completed. Task ID: {task_id}")
    print("You can verify the data in MongoDB using this task_id")
